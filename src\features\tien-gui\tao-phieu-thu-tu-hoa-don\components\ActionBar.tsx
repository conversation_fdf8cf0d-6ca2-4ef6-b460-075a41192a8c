import { Search, RefreshCw, Plus, Trash2 } from 'lucide-react';
import React from 'react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { CreateReceiptFromInvoiceFormValues } from '../schema';
import { AritoIcon } from '@/components/custom/arito';

interface ActionBarProps {
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onCreateReceiptClick?: () => void;
  onDeleteReceiptClick?: () => void;
  searchParams?: CreateReceiptFromInvoiceFormValues | null;
  totalItems?: number;
  selectedCount?: number;
}

/**
 * ActionBar component for the "Tạo phiếu thu từ hóa đơn" feature
 */
const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onCreateReceiptClick,
  onDeleteReceiptClick,
  searchParams,
  totalItems = 0,
  selectedCount = 0
}) => {
  // Determine the action text based on the searchParams
  const actionText = searchParams?.xu_ly === '1' ? 'Tạo' : searchParams?.xu_ly === '2' ? 'Xóa' : 'Tạo';
  // Get document type name
  const documentTypeName = searchParams?.ma_ct || 'hóa đơn';

  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Tạo phiếu thu từ hóa đơn</h1>
          {searchParams && (
            <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
              <div className='mr-2 size-2 rounded-full bg-red-500' />
              {actionText} từ [{documentTypeName}] ngày {searchParams.ngay_ct1} đến ngày {searchParams.ngay_ct2}
              {totalItems > 0 && <span className='ml-4 text-blue-600'>({totalItems} bản ghi)</span>}
              {selectedCount > 0 && (
                <span className='ml-4 font-semibold text-green-600'>- Đã chọn {selectedCount} hóa đơn</span>
              )}
            </span>
          )}
        </div>
      }
    >
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} /><AritoActionButton
            title='Tạo phiếu thu'
            icon={Plus}
            onClick={onCreateReceiptClick}
          />
      {searchParams &&
        (searchParams.xu_ly === '1' ? (
          <></>
        ) : (
          <AritoActionButton
            title='Xóa phiếu thu'
            icon={Trash2}
            onClick={onDeleteReceiptClick}
            disabled={!onDeleteReceiptClick || selectedCount === 0}
            variant='destructive'
          />
        ))}
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} />
      <AritoMenuButton
        items={[
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            group: 0
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={18} />,
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
